class AppConfig {
  // API Configuration
  // Override with: --dart-define=API_BASE_URL=http://127.0.0.1:8000/api (or 10.0.2.2 for Android emulator)
  static const String apiBaseUrl = String.fromEnvironment(
    'API_BASE_URL',
    defaultValue: 'https://telcobd.com/api',
  );

  // App Information
  static const String appName = 'TelcoBD';
  static const String appVersion = '1.0.0';

  // Timeout Configuration
  static const int connectionTimeout = 30000; // 30 seconds
  static const int receiveTimeout = 30000; // 30 seconds

  // Supported Operators
  static const List<String> operators = [
    'Grameenphone',
    'Robi',
    'Airtel',
    'Banglalink',
    'Teletalk',
  ];

  // Supported Payment Methods
  static const List<String> paymentMethods = [
    'bKash',
    'Nagad',
    'Rocket',
    'Wallet',
    'Manual',
  ];

  // User Roles
  static const String roleCustomer = 'customer';
  static const String roleAgent = 'agent';
  static const String roleDealer = 'dealer';
  static const String roleAdmin = 'admin';
}
