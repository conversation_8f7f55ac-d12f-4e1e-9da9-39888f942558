import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:telco_bd/config/app_config.dart';
import 'package:telco_bd/core/storage/secure_storage.dart';
import 'package:telco_bd/core/utils/constants.dart';

class ApiClient {
  late Dio _dio;
  String? _sessionToken; // in-memory token for immediate use after login

  // Singleton pattern
  static final ApiClient _instance = ApiClient._internal();

  factory ApiClient() {
    return _instance;
  }

  ApiClient._internal() {
    _initDio();
  }

  void _initDio() {
    final baseOptions = BaseOptions(
      baseUrl: AppConfig.apiBaseUrl,
      connectTimeout: Duration(milliseconds: AppConfig.connectionTimeout),
      receiveTimeout: Duration(milliseconds: AppConfig.receiveTimeout),
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      validateStatus: (status) {
        // Accept all status codes to handle them manually
        return true;
      },
    );

    _dio = Dio(baseOptions);

    // Note: Do not customize IO HttpClient adapter here to keep web support.
    // On mobile/desktop you can enable badCertificateCallback in debug builds
    // using a conditional import if needed.

    // Add interceptors for logging, auth token, etc.
    _dio.interceptors.add(
      LogInterceptor(
        request: kDebugMode,
        requestHeader: kDebugMode,
        requestBody: kDebugMode,
        responseHeader: kDebugMode,
        responseBody: kDebugMode,
        error: kDebugMode,
      ),
    );

    // Add auth token interceptor
    _dio.interceptors.add(
      InterceptorsWrapper(
        onRequest: (options, handler) async {
          // Attach Authorization header from session cache or secure storage
          String? token = _sessionToken;
          if (token == null) {
            final secureStorage = SecureStorageService();
            token = await secureStorage.getToken();
            if (token != null) {
              _sessionToken = token; // cache for this process
            }
          }
          if (token != null && token.isNotEmpty) {
            options.headers['Authorization'] = 'Bearer $token';
          }

          return handler.next(options);
        },
        onError: (DioException e, handler) {
          // Handle common errors like 401, 403, etc.
          if (e.response?.statusCode == 401) {
            // Check if this is a server-side authentication issue
            final responseData = e.response?.data;
            final isServerAuthIssue =
                responseData is Map<String, dynamic> &&
                responseData['message']?.toString().toLowerCase().contains(
                      'unauthenticated',
                    ) ==
                    true;

            if (isServerAuthIssue && _sessionToken != null) {
              // Don't clear token immediately - this might be a server issue
              // The user can manually logout if needed
            } else {
              // Handle unauthorized error - clear token and redirect to login
              SecureStorageService().clearAll();
              _sessionToken = null;
              // In a real app, we would use a global navigation key or event bus
              // to navigate to the login screen
            }
          }
          return handler.next(e);
        },
      ),
    );
  }

  // Set token for current app session (call after login)
  void setAuthToken(String token) {
    _sessionToken = token;
  }

  // Clear token (call on logout or 401 handling)
  void clearAuthToken() {
    _sessionToken = null;
  }

  // Generic GET request
  Future<dynamic> get(
    String endpoint, {
    Map<String, dynamic>? queryParameters,
  }) async {
    try {
      final response = await _dio.get(
        endpoint,
        queryParameters: queryParameters,
      );

      final status = response.statusCode ?? 0;
      if (status >= 400) {
        // Note: 401 errors are handled by the interceptor, not here

        // Surface server-provided message if available
        final data = response.data;
        if (data is Map<String, dynamic>) {
          if (data['message'] is String) {
            throw Exception(data['message']);
          }
          if (data['error'] is String) {
            throw Exception(data['error']);
          }
        }
        // Fallback
        _handleError(
          DioException(
            requestOptions: response.requestOptions,
            response: response,
            type: DioExceptionType.badResponse,
          ),
        );
      }

      return response.data;
    } on DioException catch (e) {
      // Check if this is a server-side authentication issue
      if (e.response?.statusCode == 401) {
        final responseData = e.response?.data;
        if (responseData is Map<String, dynamic> &&
            responseData['message']?.toString().toLowerCase().contains(
                  'unauthenticated',
                ) ==
                true) {
          throw Exception(
            'Authentication failed. This appears to be a server-side issue. Please contact support or try again later.',
          );
        }
      }
      _handleError(e);
    } catch (e) {
      throw Exception(AppConstants.serverErrorMessage);
    }
  }

  // Generic POST request
  Future<dynamic> post(String endpoint, {dynamic data}) async {
    try {
      if (kDebugMode) {
        print('API Request: POST $endpoint');
        print('Request Data: $data');
      }

      final response = await _dio.post(endpoint, data: data);

      if (kDebugMode) {
        print('API Response: ${response.statusCode}');
        print('Response Data: ${response.data}');
      }

      return response.data;
    } on DioException catch (e) {
      if (kDebugMode) {
        print('API Error: ${e.type}');
        print('Error Message: ${e.message}');
        print('Error Response: ${e.response?.data}');
      }

      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.sendTimeout ||
          e.type == DioExceptionType.receiveTimeout) {
        throw Exception(
          'Connection timeout. Please check your internet connection and try again.',
        );
      } else if (e.type == DioExceptionType.badResponse) {
        // Handle specific error responses
        final statusCode = e.response?.statusCode;
        final responseData = e.response?.data;

        if (responseData is Map<String, dynamic> &&
            responseData.containsKey('message')) {
          throw Exception(responseData['message']);
        } else if (responseData is Map<String, dynamic> &&
            responseData.containsKey('error')) {
          throw Exception(responseData['error']);
        } else if (statusCode == 422 &&
            responseData is Map<String, dynamic> &&
            responseData.containsKey('errors')) {
          // Handle validation errors
          final errors = responseData['errors'];
          if (errors is Map<String, dynamic>) {
            final firstError = errors.values.first;
            if (firstError is List && firstError.isNotEmpty) {
              throw Exception(firstError.first);
            }
          }
          throw Exception('Validation error. Please check your input.');
        }

        _handleError(e);
      } else {
        _handleError(e);
      }
    } catch (e) {
      if (kDebugMode) {
        print('General Error: $e');
      }
      throw Exception('An unexpected error occurred. Please try again later.');
    }
  }

  // Generic PUT request
  Future<dynamic> put(String endpoint, {dynamic data}) async {
    try {
      final response = await _dio.put(endpoint, data: data);
      return response.data;
    } on DioException catch (e) {
      _handleError(e);
    } catch (e) {
      throw Exception(AppConstants.serverErrorMessage);
    }
  }

  // Generic DELETE request
  Future<dynamic> delete(String endpoint, {dynamic data}) async {
    try {
      final response = await _dio.delete(endpoint, data: data);
      return response.data;
    } on DioException catch (e) {
      _handleError(e);
    } catch (e) {
      throw Exception(AppConstants.serverErrorMessage);
    }
  }

  // Error handling
  void _handleError(DioException e) {
    switch (e.type) {
      case DioExceptionType.connectionTimeout:
      case DioExceptionType.sendTimeout:
      case DioExceptionType.receiveTimeout:
        throw Exception(AppConstants.networkErrorMessage);
      case DioExceptionType.badResponse:
        switch (e.response?.statusCode) {
          case 400:
            throw Exception(
              e.response?.data['message'] ??
                  AppConstants.validationErrorMessage,
            );
          case 401:
            throw Exception(AppConstants.unauthorizedErrorMessage);
          case 403:
            throw Exception(
              'Forbidden. You do not have permission to access this resource.',
            );
          case 404:
            throw Exception('Resource not found.');
          case 500:
          default:
            throw Exception(AppConstants.serverErrorMessage);
        }
      case DioExceptionType.cancel:
        throw Exception('Request was cancelled');
      case DioExceptionType.unknown:
      default:
        // For unknown errors, surface a generic server error to remain
        // compatible across web and mobile without dart:io checks.
        throw Exception(AppConstants.serverErrorMessage);
    }
  }
}
