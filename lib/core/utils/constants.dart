class AppConstants {
  // Shared Preferences Keys
  static const String tokenKey = 'auth_token';
  static const String userKey = 'user_data';
  static const String roleKey = 'user_role';
  static const String languageKey = 'app_language';
  static const String themeKey = 'app_theme';

  // API Endpoints
  static const String loginEndpoint = '/auth/login';
  static const String registerEndpoint = '/auth/register';
  static const String logoutEndpoint = '/auth/logout';
  static const String profileEndpoint = '/user/profile';
  static const String dashboardEndpoint = '/dashboard';
  static const String topupEndpoint = '/topup';
  static const String bundleEndpoint = '/bundles';
  static const String walletEndpoint = '/wallet';
  static const String transactionsEndpoint = '/transactions';
  static const String operatorsEndpoint = '/operators';
  static const String paymentsEndpoint = '/payments';

  // Error Messages
  static const String networkErrorMessage = 'Network error. Please check your internet connection.';
  static const String serverErrorMessage = 'Server error. Please try again later.';
  static const String unauthorizedErrorMessage = 'Unauthorized. Please login again.';
  static const String validationErrorMessage = 'Validation error. Please check your input.';

  // Success Messages
  static const String loginSuccessMessage = 'Login successful.';
  static const String registerSuccessMessage = 'Registration successful.';
  static const String topupSuccessMessage = 'Topup successful.';
  static const String bundlePurchaseSuccessMessage = 'Bundle purchase successful.';
  static const String walletRechargeSuccessMessage = 'Wallet recharge successful.';

  // Validation Regex
  static const String phoneRegex = r'^01[3-9]\d{8}$'; // Bangladesh phone number format
  static const String emailRegex = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
  static const String passwordRegex = r'^.{6,}$'; // At least 6 characters
}
