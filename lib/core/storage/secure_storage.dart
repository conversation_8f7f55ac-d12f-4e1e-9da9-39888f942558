import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:telco_bd/core/utils/constants.dart';

class SecureStorageService {
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  
  // Singleton pattern
  static final SecureStorageService _instance = SecureStorageService._internal();
  
  factory SecureStorageService() {
    return _instance;
  }
  
  SecureStorageService._internal();
  
  // Save auth token
  Future<void> saveToken(String token) async {
    await _secureStorage.write(key: AppConstants.tokenKey, value: token);
  }
  
  // Get auth token
  Future<String?> getToken() async {
    return await _secureStorage.read(key: AppConstants.tokenKey);
  }
  
  // Delete auth token
  Future<void> deleteToken() async {
    await _secureStorage.delete(key: AppConstants.tokenKey);
  }
  
  // Save user data
  Future<void> saveUser(Map<String, dynamic> userData) async {
    await _secureStorage.write(
      key: AppConstants.userKey,
      value: jsonEncode(userData),
    );
  }
  
  // Get user data
  Future<Map<String, dynamic>?> getUser() async {
    final userString = await _secureStorage.read(key: AppConstants.userKey);
    if (userString != null) {
      return jsonDecode(userString) as Map<String, dynamic>;
    }
    return null;
  }
  
  // Delete user data
  Future<void> deleteUser() async {
    await _secureStorage.delete(key: AppConstants.userKey);
  }
  
  // Save user role
  Future<void> saveRole(String role) async {
    await _secureStorage.write(key: AppConstants.roleKey, value: role);
  }
  
  // Get user role
  Future<String?> getRole() async {
    return await _secureStorage.read(key: AppConstants.roleKey);
  }
  
  // Clear all data (for logout)
  Future<void> clearAll() async {
    await _secureStorage.deleteAll();
  }
}
