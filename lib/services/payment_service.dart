import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:telco_bd/core/network/api_client.dart';
import 'package:telco_bd/models/payment.dart';
import 'package:url_launcher/url_launcher.dart';

class PaymentService {
  final ApiClient _apiClient = ApiClient();

  /// Initialize a payment
  Future<Payment> initializePayment({
    required double amount,
    required String paymentMethod,
  }) async {
    try {
      final response = await _apiClient.post(
        '/payments/initialize',
        data: {'amount': amount, 'payment_method': paymentMethod},
      );

      if (response.data['success'] == true) {
        return Payment.fromJson(response.data['payment']);
      } else {
        throw Exception(
          response.data['message'] ?? 'Failed to initialize payment',
        );
      }
    } on DioException catch (e) {
      if (kDebugMode) {
        print('Payment initialization error: ${e.message}');
      }
      throw Exception('Failed to initialize payment: ${e.message}');
    } catch (e) {
      if (kDebugMode) {
        print('Payment initialization error: $e');
      }
      throw Exception('Failed to initialize payment: $e');
    }
  }

  /// Check payment status
  Future<Payment> checkPaymentStatus(String referenceId) async {
    try {
      final response = await _apiClient.get('/payments/status/$referenceId');

      if (response.data['success'] == true) {
        return Payment.fromJson(response.data['payment']);
      } else {
        throw Exception(
          response.data['message'] ?? 'Failed to check payment status',
        );
      }
    } on DioException catch (e) {
      if (kDebugMode) {
        print('Payment status check error: ${e.message}');
      }
      throw Exception('Failed to check payment status: ${e.message}');
    } catch (e) {
      if (kDebugMode) {
        print('Payment status check error: $e');
      }
      throw Exception('Failed to check payment status: $e');
    }
  }

  /// Launch payment URL
  Future<bool> launchPaymentUrl(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      return await launchUrl(
        Uri.parse(url),
        mode: LaunchMode.externalApplication,
      );
    } else {
      throw Exception('Could not launch payment URL: $url');
    }
  }

  /// Add funds to wallet (manual method)
  Future<Map<String, dynamic>> addFunds({
    required double amount,
    required String paymentMethod,
    required String transactionId,
  }) async {
    try {
      final response = await _apiClient.post(
        '/wallet/add',
        data: {
          'amount': amount,
          'payment_method': paymentMethod,
          'transaction_id': transactionId,
        },
      );

      if (response.data['success'] == true) {
        return {
          'success': true,
          'message': response.data['message'] ?? 'Funds added successfully',
          'transaction': response.data['transaction'],
          'user': response.data['user'],
        };
      } else {
        throw Exception(response.data['message'] ?? 'Failed to add funds');
      }
    } on DioException catch (e) {
      if (kDebugMode) {
        print('Add funds error: ${e.message}');
      }
      throw Exception('Failed to add funds: ${e.message}');
    } catch (e) {
      if (kDebugMode) {
        print('Add funds error: $e');
      }
      throw Exception('Failed to add funds: $e');
    }
  }
}
