import 'package:telco_bd/core/network/api_client.dart';
import 'package:telco_bd/core/utils/constants.dart';
import 'package:telco_bd/features/topup/models/operator_model.dart';
import 'package:telco_bd/features/transactions/models/transaction_model.dart';

class TopupService {
  final ApiClient _apiClient = ApiClient();

  // Get all operators
  Future<List<Operator>> getOperators() async {
    try {
      final response = await _apiClient.get(AppConstants.operatorsEndpoint);

      final List<dynamic> operatorsJson = response['operators'];
      return operatorsJson.map((json) => Operator.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to load operators: ${e.toString()}');
    }
  }

  // Get all packages
  Future<List<OperatorPackage>> getPackages() async {
    try {
      final response = await _apiClient.get(AppConstants.bundleEndpoint);

      final List<dynamic> packagesJson = response['packages'];
      return packagesJson.map((json) => OperatorPackage.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to load packages: ${e.toString()}');
    }
  }

  // Get packages by operator
  Future<List<OperatorPackage>> getPackagesByOperator(int operatorId) async {
    try {
      final response = await _apiClient.get('${AppConstants.bundleEndpoint}/$operatorId');

      final List<dynamic> packagesJson = response['packages'];
      return packagesJson.map((json) => OperatorPackage.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to load packages: ${e.toString()}');
    }
  }

  // Process topup
  Future<Transaction> processTopup({
    required String phoneNumber,
    required double amount,
    required int operatorId,
    required String paymentMethod,
    String? paymentReference,
  }) async {
    try {
      final response = await _apiClient.post(
        AppConstants.topupEndpoint,
        data: {
          'phone_number': phoneNumber,
          'amount': amount,
          'operator_id': operatorId,
          'payment_method': paymentMethod,
          'payment_reference': paymentReference,
        },
      );

      return Transaction.fromJson(response['transaction']);
    } catch (e) {
      throw Exception('Failed to process topup: ${e.toString()}');
    }
  }

  // Process bundle purchase
  Future<Transaction> processBundle({
    required String phoneNumber,
    required int packageId,
    required String paymentMethod,
    String? paymentReference,
  }) async {
    try {
      final response = await _apiClient.post(
        AppConstants.bundleEndpoint,
        data: {
          'phone_number': phoneNumber,
          'package_id': packageId,
          'payment_method': paymentMethod,
          'payment_reference': paymentReference,
        },
      );

      return Transaction.fromJson(response['transaction']);
    } catch (e) {
      throw Exception('Failed to process bundle purchase: ${e.toString()}');
    }
  }
}
