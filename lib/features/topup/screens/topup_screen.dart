import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:telco_bd/config/theme_config.dart';
import 'package:telco_bd/features/topup/models/operator_model.dart';
import 'package:telco_bd/features/topup/services/topup_service.dart';
import 'package:telco_bd/main.dart';

class TopupScreen extends StatefulWidget {
  final String? initialTab;
  final Operator? selectedOperator;

  const TopupScreen({super.key, this.initialTab, this.selectedOperator});

  @override
  State<TopupScreen> createState() => _TopupScreenState();
}

class _TopupScreenState extends State<TopupScreen> with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _amountController = TextEditingController();

  late TabController _tabController;

  Operator? _selectedOperator;
  String? _paymentMethod;
  String? _paymentReference;

  bool _isLoading = false;
  String? _error;

  List<Operator> _operators = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // Set initial tab if provided
    if (widget.initialTab == 'drive_offer') {
      _tabController.animateTo(1);
    }

    // Set selected operator if provided
    if (widget.selectedOperator != null) {
      _selectedOperator = widget.selectedOperator;
    }

    _loadOperators();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _amountController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadOperators() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final operators = await TopupService().getOperators();
      setState(() {
        _operators = operators;

        // Only set the selected operator if it wasn't already set
        if (_selectedOperator == null && operators.isNotEmpty) {
          _selectedOperator = operators.first;
        } else if (_selectedOperator != null && operators.isNotEmpty) {
          // If we have a selected operator, make sure it's from the loaded list
          final matchingOperator = operators.firstWhere(
            (op) => op.id == _selectedOperator!.id,
            orElse: () => operators.first,
          );
          _selectedOperator = matchingOperator;
        }
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _processTopup() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedOperator == null) {
      setState(() {
        _error = 'Please select an operator';
      });
      return;
    }

    if (_paymentMethod == null) {
      setState(() {
        _error = 'Please select a payment method';
      });
      return;
    }

    if (_paymentMethod != 'wallet' && (_paymentReference == null || _paymentReference!.isEmpty)) {
      setState(() {
        _error = 'Please enter payment reference';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      await TopupService().processTopup(
        phoneNumber: _phoneController.text,
        amount: double.parse(_amountController.text),
        operatorId: _selectedOperator!.id,
        paymentMethod: _paymentMethod!,
        paymentReference: _paymentReference,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Topup successful'),
            backgroundColor: AppTheme.secondaryColor,
          ),
        );

        // Clear form
        _phoneController.clear();
        _amountController.clear();
        _paymentReference = null;
      }
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get auth provider
    Provider.of<AuthProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Mobile Topup'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Regular Topup'),
            Tab(text: 'Drive Offer'),
          ],
        ),
      ),
      body: _isLoading && _operators.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : _error != null && _operators.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Error: $_error',
                        style: const TextStyle(color: AppTheme.errorColor),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _loadOperators,
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : TabBarView(
                  controller: _tabController,
                  children: [
                    // Regular Topup Tab
                    SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        // Operator selection
                        const Text(
                          'Select Operator',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        DropdownButtonFormField<Operator>(
                          value: _selectedOperator,
                          decoration: const InputDecoration(
                            border: OutlineInputBorder(),
                            contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                          ),
                          items: _operators.map((operator) {
                            return DropdownMenuItem<Operator>(
                              value: operator,
                              child: Row(
                                children: [
                                  // Operator logo would go here
                                  const Icon(Icons.sim_card),
                                  const SizedBox(width: 8),
                                  Text(operator.name),
                                ],
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedOperator = value;
                            });
                          },
                        ),
                        const SizedBox(height: 16),

                        // Phone number
                        TextFormField(
                          controller: _phoneController,
                          keyboardType: TextInputType.phone,
                          decoration: const InputDecoration(
                            labelText: 'Phone Number',
                            hintText: '01XXXXXXXXX',
                            prefixIcon: Icon(Icons.phone_android),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter phone number';
                            }
                            if (!RegExp(r'^01[3-9]\d{8}$').hasMatch(value)) {
                              return 'Please enter a valid phone number';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 16),

                        // Amount
                        TextFormField(
                          controller: _amountController,
                          keyboardType: TextInputType.number,
                          decoration: const InputDecoration(
                            labelText: 'Amount (BDT)',
                            hintText: '100',
                            prefixIcon: Icon(Icons.attach_money),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter amount';
                            }
                            final amount = double.tryParse(value);
                            if (amount == null) {
                              return 'Please enter a valid amount';
                            }
                            if (amount < 10) {
                              return 'Minimum amount is 10 BDT';
                            }
                            if (amount > 1000) {
                              return 'Maximum amount is 1000 BDT';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 24),

                        // Payment method
                        const Text(
                          'Select Payment Method',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          children: [
                            _buildPaymentMethodChip('wallet', 'Wallet', Icons.account_balance_wallet),
                            _buildPaymentMethodChip('bkash', 'bKash', Icons.payment),
                            _buildPaymentMethodChip('nagad', 'Nagad', Icons.payment),
                            _buildPaymentMethodChip('rocket', 'Rocket', Icons.payment),
                          ],
                        ),
                        const SizedBox(height: 16),

                        // Payment reference (for mobile banking)
                        if (_paymentMethod != null && _paymentMethod != 'wallet')
                          TextFormField(
                            decoration: InputDecoration(
                              labelText: '$_paymentMethod Transaction ID',
                              hintText: 'Enter transaction ID',
                              prefixIcon: const Icon(Icons.receipt_long),
                            ),
                            onChanged: (value) {
                              setState(() {
                                _paymentReference = value;
                              });
                            },
                            validator: (value) {
                              if (_paymentMethod != 'wallet' && (value == null || value.isEmpty)) {
                                return 'Please enter transaction ID';
                              }
                              return null;
                            },
                          ),

                        const SizedBox(height: 24),

                        // Error message
                        if (_error != null)
                          Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: Text(
                              _error!,
                              style: const TextStyle(color: AppTheme.errorColor),
                              textAlign: TextAlign.center,
                            ),
                          ),

                        // Submit button
                        ElevatedButton(
                          onPressed: _isLoading ? null : _processTopup,
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Text('Recharge Now'),
                        ),
                      ],
                    ),
                  ),
                ),

                    // Drive Offer Tab
                    SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Form(
                        key: GlobalKey<FormState>(),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            // Operator selection
                            const Text(
                              'Select Operator',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            DropdownButtonFormField<Operator>(
                              value: _selectedOperator,
                              decoration: const InputDecoration(
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                              ),
                              items: _operators.map((operator) {
                                return DropdownMenuItem<Operator>(
                                  value: operator,
                                  child: Row(
                                    children: [
                                      const Icon(Icons.sim_card),
                                      const SizedBox(width: 8),
                                      Text(operator.name),
                                    ],
                                  ),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedOperator = value;
                                });
                              },
                            ),
                            const SizedBox(height: 16),

                            // Phone number
                            TextFormField(
                              keyboardType: TextInputType.phone,
                              decoration: const InputDecoration(
                                labelText: 'Phone Number',
                                hintText: '01XXXXXXXXX',
                                prefixIcon: Icon(Icons.phone_android),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'Please enter phone number';
                                }
                                if (!RegExp(r'^01[3-9]\d{8}$').hasMatch(value)) {
                                  return 'Please enter a valid phone number';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),

                            // Drive Offer Packages
                            const Text(
                              'Select Drive Offer Package',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),

                            // Drive Offer Packages List
                            _buildDriveOfferPackage('Daily Drive 2GB', '৳33', '2GB', '24 Hours'),
                            const SizedBox(height: 8),
                            _buildDriveOfferPackage('Weekly Drive 5GB', '৳99', '5GB', '7 Days'),
                            const SizedBox(height: 8),
                            _buildDriveOfferPackage('Monthly Drive 10GB', '৳198', '10GB', '30 Days'),
                            const SizedBox(height: 8),
                            _buildDriveOfferPackage('Monthly Drive 20GB', '৳299', '20GB', '30 Days'),

                            const SizedBox(height: 24),

                            // Payment method
                            const Text(
                              'Select Payment Method',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Wrap(
                              spacing: 8,
                              children: [
                                _buildPaymentMethodChip('wallet', 'Wallet', Icons.account_balance_wallet),
                                _buildPaymentMethodChip('bkash', 'bKash', Icons.payment),
                                _buildPaymentMethodChip('nagad', 'Nagad', Icons.payment),
                                _buildPaymentMethodChip('rocket', 'Rocket', Icons.payment),
                              ],
                            ),
                            const SizedBox(height: 24),

                            // Submit button
                            ElevatedButton(
                              onPressed: () {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('Drive offer purchase successful'),
                                    backgroundColor: AppTheme.secondaryColor,
                                  ),
                                );
                              },
                              style: ElevatedButton.styleFrom(
                                padding: const EdgeInsets.symmetric(vertical: 16),
                              ),
                              child: const Text('Purchase Drive Offer'),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
    );
  }

  Widget _buildDriveOfferPackage(String name, String price, String data, String validity) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          // Select this package
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                Colors.orange.withAlpha(25),
              ],
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Stack(
                  alignment: Alignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppTheme.primaryColor.withAlpha(25),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.local_offer,
                        size: 20,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    Positioned(
                      right: 0,
                      bottom: 0,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: Colors.orange,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.directions_car,
                          size: 10,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          _buildFeatureChip(data),
                          const SizedBox(width: 6),
                          _buildFeatureChip(validity),
                        ],
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.withAlpha(50),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    price,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureChip(String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300, width: 0.5),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 10,
          fontWeight: FontWeight.w500,
          color: Colors.grey.shade700,
        ),
      ),
    );
  }

  Widget _buildPaymentMethodChip(String value, String label, IconData icon) {
    final isSelected = _paymentMethod == value;

    return ChoiceChip(
      label: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 18,
            color: isSelected ? Colors.white : AppTheme.textPrimaryColor,
          ),
          const SizedBox(width: 4),
          Text(label),
        ],
      ),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _paymentMethod = selected ? value : null;
          if (value == 'wallet') {
            _paymentReference = 'wallet';
          } else {
            _paymentReference = null;
          }
        });
      },
      backgroundColor: Colors.white,
      selectedColor: AppTheme.primaryColor,
      labelStyle: TextStyle(
        color: isSelected ? Colors.white : AppTheme.textPrimaryColor,
      ),
    );
  }
}
