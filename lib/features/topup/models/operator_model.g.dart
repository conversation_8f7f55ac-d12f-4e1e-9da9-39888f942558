// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'operator_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Operator _$OperatorFromJson(Map<String, dynamic> json) => Operator(
  id: (json['id'] as num).toInt(),
  name: json['name'] as String,
  code: json['code'] as String,
  logo: json['logo'] as String,
  isActive: json['isActive'] as bool,
  packages:
      (json['packages'] as List<dynamic>)
          .map((e) => OperatorPackage.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$OperatorToJson(Operator instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'code': instance.code,
  'logo': instance.logo,
  'isActive': instance.isActive,
  'packages': instance.packages,
};

OperatorPackage _$OperatorPackageFromJson(Map<String, dynamic> json) =>
    OperatorPackage(
      id: (json['id'] as num).toInt(),
      name: json['name'] as String,
      type: json['type'] as String,
      description: json['description'] as String,
      price: (json['price'] as num).toDouble(),
      validity: (json['validity'] as num).toInt(),
      isActive: json['isActive'] as bool,
      operatorId: (json['operatorId'] as num).toInt(),
    );

Map<String, dynamic> _$OperatorPackageToJson(OperatorPackage instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'description': instance.description,
      'price': instance.price,
      'validity': instance.validity,
      'isActive': instance.isActive,
      'operatorId': instance.operatorId,
    };
