import 'package:json_annotation/json_annotation.dart';

part 'operator_model.g.dart';

@JsonSerializable()
class Operator {
  final int id;
  final String name;
  final String code;
  final String logo;
  final bool isActive;
  final List<OperatorPackage> packages;

  Operator({
    required this.id,
    required this.name,
    required this.code,
    required this.logo,
    required this.isActive,
    required this.packages,
  });

  // Custom fromJson to be tolerant of API variations
  factory Operator.fromJson(Map<String, dynamic> json) {
    final packagesJson = json['packages'];
    List<OperatorPackage> parsedPackages = [];
    if (packagesJson is List) {
      parsedPackages =
          packagesJson
              .whereType<Map<String, dynamic>>()
              .map(OperatorPackage.fromJson)
              .toList();
    }

    return Operator(
      id:
          json['id'] is String
              ? int.tryParse(json['id']) ?? 0
              : (json['id'] ?? 0),
      name: (json['name'] ?? '').toString(),
      code: (json['code'] ?? '').toString(),
      logo: (json['logo'] ?? '').toString(),
      isActive: (json['is_active'] ?? json['isActive'] ?? true) == true,
      packages: parsedPackages,
    );
  }

  Map<String, dynamic> toJson() => _$OperatorToJson(this);
}

@JsonSerializable()
class OperatorPackage {
  final int id;
  final String name;
  final String type; // data, minutes, sms, combo
  final String description;
  final double price;
  final int validity; // in days
  final bool isActive;
  final int operatorId;

  OperatorPackage({
    required this.id,
    required this.name,
    required this.type,
    required this.description,
    required this.price,
    required this.validity,
    required this.isActive,
    required this.operatorId,
  });

  // Custom fromJson to support snake_case keys and flexible types
  factory OperatorPackage.fromJson(Map<String, dynamic> json) {
    double parseDouble(dynamic v) {
      if (v is int) return v.toDouble();
      if (v is double) return v;
      if (v is String) return double.tryParse(v) ?? 0.0;
      return 0.0;
    }

    return OperatorPackage(
      id:
          json['id'] is String
              ? int.tryParse(json['id']) ?? 0
              : (json['id'] ?? 0),
      name: (json['name'] ?? '').toString(),
      type: (json['type'] ?? '').toString(),
      description: (json['description'] ?? '').toString(),
      price: parseDouble(json['price']),
      validity:
          json['validity'] is String
              ? int.tryParse(json['validity']) ?? 0
              : (json['validity'] ?? 0),
      isActive: (json['is_active'] ?? json['isActive'] ?? true) == true,
      operatorId:
          json['operator_id'] is String
              ? int.tryParse(json['operator_id']) ?? 0
              : (json['operator_id'] ?? json['operatorId'] ?? 0),
    );
  }

  Map<String, dynamic> toJson() => _$OperatorPackageToJson(this);
}
