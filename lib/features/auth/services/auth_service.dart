import 'package:flutter/foundation.dart';
import 'package:telco_bd/core/network/api_client.dart';
import 'package:telco_bd/core/storage/secure_storage.dart';
import 'package:telco_bd/core/utils/constants.dart';
import 'package:telco_bd/features/auth/models/user_model.dart';
import 'package:jwt_decoder/jwt_decoder.dart';

class AuthService {
  final ApiClient _apiClient = ApiClient();
  final SecureStorageService _secureStorage = SecureStorageService();

  // Singleton pattern
  static final AuthService _instance = AuthService._internal();

  factory AuthService() {
    return _instance;
  }

  AuthService._internal();

  // Login user with either phone or email as identifier
  Future<User> login(String identifier, String password) async {
    try {
      final bool isPhone = RegExp(AppConstants.phoneRegex).hasMatch(identifier);
      // Backend expects 'login' (phone or email); include specific key for legacy compatibility
      final Map<String, dynamic> payload = {
        'login': identifier,
        'password': password,
        if (isPhone) 'phone': identifier,
      };

      final response = await _apiClient.post(
        AppConstants.loginEndpoint,
        data: payload,
      );

    // Check if response is successful per contract
      if (response is Map<String, dynamic> && response['success'] == true) {
        // Save token and user data
        if (response.containsKey('token')) {
      final String token = response['token'].toString().trim();
      await _secureStorage.saveToken(token);
      _apiClient.setAuthToken(token);
        } else {
          throw Exception('Authentication token not found in response');
        }

        if (response.containsKey('user') &&
            response['user'] is Map<String, dynamic>) {
          await _secureStorage.saveUser(response['user']);

          if (response['user'].containsKey('role')) {
            await _secureStorage.saveRole(response['user']['role']);
          }

          return User.fromJson(response['user']);
        } else {
          throw Exception('User data not found in response');
        }
      } else if (response is Map<String, dynamic>) {
        // Try to surface detailed errors from 'errors' if present
        if (response['errors'] is Map<String, dynamic>) {
          final errors = response['errors'] as Map<String, dynamic>;
          // Prefer specific keys
          final keys = ['login', 'phone', 'email'];
          for (final k in keys) {
            final val = errors[k];
            if (val is List && val.isNotEmpty) {
              final msg = val.first.toString();
              throw Exception(msg);
            }
          }
          final first = errors.values.isNotEmpty ? errors.values.first : null;
          if (first is List && first.isNotEmpty) {
            throw Exception(first.first.toString());
          }
        }
        if (response['message'] is String) {
          throw Exception(response['message']);
        }
        throw Exception('Login failed');
      } else {
        throw Exception('Invalid response format from server');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Login error: $e');
      }
      rethrow;
    }
  }

  // Register user
  Future<User> register(
    String name,
    String phone,
    String email,
    String password,
  ) async {
    try {
      final response = await _apiClient.post(
        AppConstants.registerEndpoint,
        data: {
          'name': name,
          'phone': phone,
          'email': email,
          'password': password,
          'password_confirmation': password,
        },
      );

    // Check if response is successful
      if (response is Map<String, dynamic> &&
          response.containsKey('success') &&
          response['success'] == true) {
        // Save token and user data
        if (response.containsKey('token')) {
      final String token = response['token'].toString().trim();
      await _secureStorage.saveToken(token);
      _apiClient.setAuthToken(token);
        } else {
          throw Exception('Authentication token not found in response');
        }

        if (response.containsKey('user') &&
            response['user'] is Map<String, dynamic>) {
          await _secureStorage.saveUser(response['user']);

          if (response['user'].containsKey('role')) {
            await _secureStorage.saveRole(response['user']['role']);
          }

          return User.fromJson(response['user']);
        } else {
          throw Exception('User data not found in response');
        }
      } else if (response is Map<String, dynamic> &&
          response.containsKey('message')) {
        throw Exception(response['message']);
      } else {
        throw Exception('Invalid response format from server');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Registration error: $e');
      }
      rethrow;
    }
  }

  // Logout user
  Future<void> logout() async {
    try {
      await _apiClient.post(AppConstants.logoutEndpoint);
    } catch (e) {
      // Ignore errors during logout
    } finally {
      // Clear local storage
      await _secureStorage.clearAll();
      _apiClient.clearAuthToken();
    }
  }

  // Get current user
  Future<User?> getCurrentUser() async {
    try {
      final userData = await _secureStorage.getUser();
      if (userData != null) {
        return User.fromJson(userData);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    try {
      final token = await _secureStorage.getToken();
      if (token == null) {
        return false;
      }

      // If token looks like a JWT, validate expiration; otherwise, assume valid
      final looksLikeJwt = token.contains('.');
      if (looksLikeJwt) {
        try {
          final isExpired = JwtDecoder.isExpired(token);
          if (isExpired) {
            await _secureStorage.clearAll();
            _apiClient.clearAuthToken();
            return false;
          }
        } catch (_) {
          // If parsing fails, continue treating token as valid
        }
      }

      // Prime ApiClient with stored token for subsequent requests
      _apiClient.setAuthToken(token);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Get user role
  Future<String?> getUserRole() async {
    return await _secureStorage.getRole();
  }

  // Update user profile
  Future<User> updateProfile(Map<String, dynamic> userData) async {
    try {
      final response = await _apiClient.put(
        AppConstants.profileEndpoint,
        data: userData,
      );

      // Update stored user data
      await _secureStorage.saveUser(response['user']);

      return User.fromJson(response['user']);
    } catch (e) {
      rethrow;
    }
  }

  // Change password
  Future<void> changePassword(
    String currentPassword,
    String newPassword,
  ) async {
    try {
      await _apiClient.put(
        '${AppConstants.profileEndpoint}/password',
        data: {
          'current_password': currentPassword,
          'password': newPassword,
          'password_confirmation': newPassword,
        },
      );
    } catch (e) {
      rethrow;
    }
  }
}
