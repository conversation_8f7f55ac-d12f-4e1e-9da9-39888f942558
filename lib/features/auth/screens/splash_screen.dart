import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:telco_bd/config/app_config.dart';
import 'package:telco_bd/config/theme_config.dart';
import 'package:telco_bd/features/auth/screens/login_screen.dart';
import 'package:telco_bd/features/home/<USER>/home_screen.dart';
import 'package:telco_bd/main.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    // Initialize auth provider
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.initialize();

    // Navigate to appropriate screen after initialization
    if (mounted) {
      await Future.delayed(const Duration(seconds: 2));

      if (!mounted) return;

      if (authProvider.isLoggedIn) {
        // Navigate to home screen if logged in
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const HomeScreen()),
        );
      } else {
        // Navigate to login screen if not logged in
        Navigator.pushReplacement(
          context,
          MaterialPageRoute(builder: (context) => const LoginScreen()),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.primaryColor,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Logo or app icon
            const Icon(
              Icons.phone_android,
              size: 80,
              color: Colors.white,
            ),
            const SizedBox(height: 24),
            // App name
            Text(
              AppConfig.appName,
              style: const TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            // Tagline
            const Text(
              'Mobile TopUp Solution for Bangladesh',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 48),
            // Loading indicator
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}
