import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class User {
  final int id;
  final String name;
  final String email;
  final String phone;
  final String role;
  final String? address;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'profile_image')
  final String? profileImage;

  final double balance;
  final double commission;

  @Json<PERSON>ey(name: 'parent_id')
  final int? parentId; // For agent/dealer hierarchy

  @JsonKey(name: 'created_at')
  final DateTime createdAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final DateTime updatedAt;

  User({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.role,
    this.address,
    this.profileImage,
    required this.balance,
    required this.commission,
    this.parentId,
    required this.createdAt,
    required this.updatedAt,
  });

  // Custom fromJson method to handle API response format
  factory User.fromJson(Map<String, dynamic> json) {
    // Handle both snake_case and camelCase field names
    return User(
      id: json['id'],
      name: json['name'],
      email: json['email'],
      phone: json['phone'],
      role: json['role'],
      address: json['address'],
      profileImage: json['profile_image'] ?? json['profileImage'],
      balance:
          (json['balance'] is String)
              ? double.tryParse(json['balance']) ?? 0.0
              : (json['balance'] is int)
              ? (json['balance'] as int).toDouble()
              : (json['balance'] is double)
              ? json['balance']
              : 0.0,
      commission:
          (json['commission'] is String)
              ? double.tryParse(json['commission']) ?? 0.0
              : (json['commission'] is int)
              ? (json['commission'] as int).toDouble()
              : (json['commission'] is double)
              ? json['commission']
              : 0.0,
      parentId: json['parent_id'] ?? json['parentId'],
      createdAt:
          json['created_at'] != null
              ? DateTime.parse(json['created_at'])
              : (json['createdAt'] != null
                  ? DateTime.parse(json['createdAt'])
                  : DateTime.now()),
      updatedAt:
          json['updated_at'] != null
              ? DateTime.parse(json['updated_at'])
              : (json['updatedAt'] != null
                  ? DateTime.parse(json['updatedAt'])
                  : DateTime.now()),
    );
  }

  Map<String, dynamic> toJson() => _$UserToJson(this);

  // Check if user is customer
  bool get isCustomer => role == 'customer';

  // Check if user is agent
  bool get isAgent => role == 'agent';

  // Check if user is dealer
  bool get isDealer => role == 'dealer';

  // Check if user is admin
  bool get isAdmin => role == 'admin';

  // Create a copy of the user with updated fields
  User copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? role,
    String? address,
    String? profileImage,
    double? balance,
    double? commission,
    int? parentId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return User(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      role: role ?? this.role,
      address: address ?? this.address,
      profileImage: profileImage ?? this.profileImage,
      balance: balance ?? this.balance,
      commission: commission ?? this.commission,
      parentId: parentId ?? this.parentId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
