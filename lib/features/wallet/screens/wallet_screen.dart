import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:telco_bd/config/theme_config.dart';
import 'package:telco_bd/features/transactions/models/transaction_model.dart';
import 'package:telco_bd/features/wallet/models/wallet_model.dart';
import 'package:telco_bd/features/wallet/services/wallet_service.dart';
import 'package:telco_bd/main.dart';
import 'package:intl/intl.dart';

class WalletScreen extends StatefulWidget {
  const WalletScreen({super.key});

  @override
  State<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  Wallet? _wallet;
  List<Transaction> _transactions = [];

  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadWallet();
    _loadTransactions();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadWallet() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final wallet = await WalletService().getWallet();
      setState(() {
        _wallet = wallet;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadTransactions() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final transactions = await WalletService().getTransactions();
      setState(() {
        _transactions = transactions;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showDepositDialog() {
    final amountController = TextEditingController();
    String? selectedPaymentMethod;
    String? selectedPaymentReference;

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text('Deposit to Wallet'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextField(
                      controller: amountController,
                      keyboardType: TextInputType.number,
                      decoration: const InputDecoration(
                        labelText: 'Amount (BDT)',
                        hintText: '100',
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Payment Method',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      children: [
                        ChoiceChip(
                          label: const Text('bKash'),
                          selected: selectedPaymentMethod == 'bkash',
                          onSelected: (selected) {
                            setState(() {
                              selectedPaymentMethod = selected ? 'bkash' : null;
                            });
                          },
                        ),
                        ChoiceChip(
                          label: const Text('Nagad'),
                          selected: selectedPaymentMethod == 'nagad',
                          onSelected: (selected) {
                            setState(() {
                              selectedPaymentMethod = selected ? 'nagad' : null;
                            });
                          },
                        ),
                        ChoiceChip(
                          label: const Text('Rocket'),
                          selected: selectedPaymentMethod == 'rocket',
                          onSelected: (selected) {
                            setState(() {
                              selectedPaymentMethod = selected ? 'rocket' : null;
                            });
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    if (selectedPaymentMethod != null)
                      TextField(
                        decoration: InputDecoration(
                          labelText: '$selectedPaymentMethod Transaction ID',
                          hintText: 'Enter transaction ID',
                        ),
                        onChanged: (value) {
                          selectedPaymentReference = value;
                        },
                      ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () async {
                    if (amountController.text.isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Please enter amount'),
                          backgroundColor: AppTheme.errorColor,
                        ),
                      );
                      return;
                    }

                    if (selectedPaymentMethod == null) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Please select payment method'),
                          backgroundColor: AppTheme.errorColor,
                        ),
                      );
                      return;
                    }

                    if (selectedPaymentReference == null || selectedPaymentReference!.isEmpty) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('Please enter transaction ID'),
                          backgroundColor: AppTheme.errorColor,
                        ),
                      );
                      return;
                    }

                    Navigator.of(context).pop();

                    setState(() {
                      _isLoading = true;
                    });

                    try {
                      await WalletService().deposit(
                        amount: double.parse(amountController.text),
                        paymentMethod: selectedPaymentMethod!,
                        paymentReference: selectedPaymentReference!,
                      );

                      // Reload wallet and transactions
                      await _loadWallet();
                      await _loadTransactions();

                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Deposit successful'),
                            backgroundColor: AppTheme.secondaryColor,
                          ),
                        );
                      }
                    } catch (e) {
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text('Deposit failed: ${e.toString()}'),
                            backgroundColor: AppTheme.errorColor,
                          ),
                        );
                      }
                    } finally {
                      setState(() {
                        _isLoading = false;
                      });
                    }
                  },
                  child: const Text('Deposit'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // Get auth provider
    Provider.of<AuthProvider>(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Wallet'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Overview'),
            Tab(text: 'Transactions'),
          ],
        ),
      ),
      body: _isLoading && _wallet == null
          ? const Center(child: CircularProgressIndicator())
          : _error != null && _wallet == null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Error: $_error',
                        style: const TextStyle(color: AppTheme.errorColor),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: () {
                          _loadWallet();
                          _loadTransactions();
                        },
                        child: const Text('Retry'),
                      ),
                    ],
                  ),
                )
              : TabBarView(
                  controller: _tabController,
                  children: [
                    // Overview tab
                    _buildOverviewTab(),

                    // Transactions tab
                    _buildTransactionsTab(),
                  ],
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showDepositDialog,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildOverviewTab() {
    if (_wallet == null) {
      return const Center(child: Text('No wallet data available'));
    }

    final currencyFormat = NumberFormat.currency(
      symbol: '৳',
      decimalDigits: 2,
    );

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Balance card
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            color: AppTheme.primaryColor,
            child: Padding(
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Current Balance',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    currencyFormat.format(_wallet!.balance),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Statistics
          const Text(
            'Statistics',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildStatCard(
            title: 'Total Deposit',
            value: _wallet!.totalDeposit,
            icon: Icons.arrow_downward,
            color: AppTheme.secondaryColor,
          ),
          const SizedBox(height: 8),
          _buildStatCard(
            title: 'Total Withdrawal',
            value: _wallet!.totalWithdrawal,
            icon: Icons.arrow_upward,
            color: AppTheme.errorColor,
          ),
          const SizedBox(height: 8),
          _buildStatCard(
            title: 'Total Commission',
            value: _wallet!.totalCommission,
            icon: Icons.monetization_on,
            color: Colors.amber,
          ),
          const SizedBox(height: 8),
          _buildStatCard(
            title: 'Total Spent',
            value: _wallet!.totalSpent,
            icon: Icons.shopping_cart,
            color: Colors.purple,
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required double value,
    required IconData icon,
    required Color color,
  }) {
    final currencyFormat = NumberFormat.currency(
      symbol: '৳',
      decimalDigits: 2,
    );

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withAlpha(25),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    currencyFormat.format(value),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionsTab() {
    if (_transactions.isEmpty) {
      return const Center(child: Text('No transactions available'));
    }

    final currencyFormat = NumberFormat.currency(
      symbol: '৳',
      decimalDigits: 2,
    );

    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: _transactions.length,
      itemBuilder: (context, index) {
        final transaction = _transactions[index];

        IconData icon;
        Color color;
        String title;

        if (transaction.isTopup) {
          icon = Icons.phone_android;
          color = AppTheme.primaryColor;
          title = 'Mobile Topup';
        } else if (transaction.isBundle) {
          icon = Icons.data_usage;
          color = AppTheme.primaryColor;
          title = 'Bundle Purchase';
        } else if (transaction.isCommission) {
          icon = Icons.monetization_on;
          color = Colors.amber;
          title = 'Commission';
        } else if (transaction.isWalletRecharge) {
          icon = Icons.account_balance_wallet;
          color = AppTheme.secondaryColor;
          title = 'Wallet Recharge';
        } else {
          icon = Icons.swap_horiz;
          color = Colors.grey;
          title = 'Transaction';
        }

        return Card(
          margin: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withAlpha(25),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: color,
              ),
            ),
            title: Text(title),
            subtitle: Text(
              transaction.phoneNumber ?? 'N/A',
              style: const TextStyle(
                fontSize: 12,
                color: AppTheme.textSecondaryColor,
              ),
            ),
            trailing: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  currencyFormat.format(transaction.amount),
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: transaction.isCommission || transaction.isWalletRecharge
                        ? AppTheme.secondaryColor
                        : AppTheme.textPrimaryColor,
                  ),
                ),
                Text(
                  DateFormat('MMM d, y').format(transaction.createdAt),
                  style: const TextStyle(
                    fontSize: 12,
                    color: AppTheme.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
