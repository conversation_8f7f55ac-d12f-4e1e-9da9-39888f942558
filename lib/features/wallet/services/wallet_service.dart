import 'package:telco_bd/core/network/api_client.dart';
import 'package:telco_bd/core/utils/constants.dart';
import 'package:telco_bd/features/transactions/models/transaction_model.dart';
import 'package:telco_bd/features/wallet/models/wallet_model.dart';

class WalletService {
  final ApiClient _apiClient = ApiClient();

  // Get wallet
  Future<Wallet> getWallet() async {
    try {
      final response = await _apiClient.get(AppConstants.walletEndpoint);

      return Wallet.fromJson(response['wallet']);
    } catch (e) {
      throw Exception('Failed to load wallet: ${e.toString()}');
    }
  }

  // Get wallet transactions
  Future<List<Transaction>> getTransactions() async {
    try {
      final response = await _apiClient.get('${AppConstants.walletEndpoint}/transactions');

      final List<dynamic> transactionsJson = response['transactions']['data'];
      return transactionsJson.map((json) => Transaction.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to load transactions: ${e.toString()}');
    }
  }

  // Deposit to wallet
  Future<Wallet> deposit({
    required double amount,
    required String paymentMethod,
    required String paymentReference,
  }) async {
    try {
      final response = await _apiClient.post(
        '${AppConstants.walletEndpoint}/deposit',
        data: {
          'amount': amount,
          'payment_method': paymentMethod,
          'payment_reference': paymentReference,
        },
      );

      return Wallet.fromJson(response['wallet']);
    } catch (e) {
      throw Exception('Failed to deposit: ${e.toString()}');
    }
  }

  // Withdraw from wallet
  Future<Wallet> withdraw({
    required double amount,
    required String paymentMethod,
    required String paymentReference,
  }) async {
    try {
      final response = await _apiClient.post(
        '${AppConstants.walletEndpoint}/withdraw',
        data: {
          'amount': amount,
          'payment_method': paymentMethod,
          'payment_reference': paymentReference,
        },
      );

      return Wallet.fromJson(response['wallet']);
    } catch (e) {
      throw Exception('Failed to withdraw: ${e.toString()}');
    }
  }
}
