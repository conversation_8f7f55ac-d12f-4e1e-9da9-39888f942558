import 'package:json_annotation/json_annotation.dart';

part 'wallet_model.g.dart';

@JsonSerializable()
class Wallet {
  final int id;
  final int userId;
  final double balance;
  final double totalDeposit;
  final double totalWithdrawal;
  final double totalCommission;
  final double totalSpent;
  final DateTime createdAt;
  final DateTime updatedAt;

  Wallet({
    required this.id,
    required this.userId,
    required this.balance,
    required this.totalDeposit,
    required this.totalWithdrawal,
    required this.totalCommission,
    required this.totalSpent,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Wallet.fromJson(Map<String, dynamic> json) => _$WalletFromJson(json);
  
  Map<String, dynamic> toJson() => _$WalletToJson(this);
  
  // Create a copy of the wallet with updated fields
  Wallet copyWith({
    int? id,
    int? userId,
    double? balance,
    double? totalDeposit,
    double? totalWithdrawal,
    double? totalCommission,
    double? totalSpent,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Wallet(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      balance: balance ?? this.balance,
      totalDeposit: totalDeposit ?? this.totalDeposit,
      totalWithdrawal: totalWithdrawal ?? this.totalWithdrawal,
      totalCommission: totalCommission ?? this.totalCommission,
      totalSpent: totalSpent ?? this.totalSpent,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
