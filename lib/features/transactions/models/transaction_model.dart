import 'package:json_annotation/json_annotation.dart';

part 'transaction_model.g.dart';

@JsonSerializable()
class Transaction {
  final int id;
  final String type; // topup, bundle, commission, wallet_recharge
  final String status; // pending, completed, failed
  final double amount;
  final double? commission;
  final int userId;
  final int? agentId;
  final int? dealerId;
  final String? phoneNumber;
  final int? operatorId;
  final int? packageId;
  final String? paymentMethod;
  final String? paymentReference;
  final String? transactionReference;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  Transaction({
    required this.id,
    required this.type,
    required this.status,
    required this.amount,
    this.commission,
    required this.userId,
    this.agentId,
    this.dealerId,
    this.phoneNumber,
    this.operatorId,
    this.packageId,
    this.paymentMethod,
    this.paymentReference,
    this.transactionReference,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  // Custom fromJson that supports snake_case keys and type coercion
  factory Transaction.fromJson(Map<String, dynamic> json) {
    double parseDouble(dynamic v) {
      if (v is int) return v.toDouble();
      if (v is double) return v;
      if (v is String) return double.tryParse(v) ?? 0.0;
      return 0.0;
    }

    int? parseInt(dynamic v) {
      if (v == null) return null;
      if (v is int) return v;
      if (v is String) return int.tryParse(v);
      return null;
    }

    String? str(dynamic v) => v?.toString();

    DateTime parseDate(dynamic v) {
      if (v is DateTime) return v;
      if (v is String) {
        final parsed = DateTime.tryParse(v);
        if (parsed != null) return parsed;
      }
      return DateTime.now();
    }

    return Transaction(
      id:
          json['id'] is String
              ? int.tryParse(json['id']) ?? 0
              : (json['id'] ?? 0),
      type: (json['type'] ?? '').toString(),
      status: (json['status'] ?? '').toString(),
      amount: parseDouble(json['amount']),
      commission:
          json.containsKey('commission')
              ? parseDouble(json['commission'])
              : null,
      userId:
          json['user_id'] is String
              ? int.tryParse(json['user_id']) ?? 0
              : (json['user_id'] ?? json['userId'] ?? 0),
      agentId: parseInt(json['agent_id'] ?? json['agentId']),
      dealerId: parseInt(json['dealer_id'] ?? json['dealerId']),
      phoneNumber: str(json['phone_number'] ?? json['phoneNumber']),
      operatorId: parseInt(json['operator_id'] ?? json['operatorId']),
      packageId: parseInt(json['package_id'] ?? json['packageId']),
      paymentMethod: str(json['payment_method'] ?? json['paymentMethod']),
      paymentReference: str(
        json['payment_reference'] ??
            json['paymentReference'] ??
            json['reference_id'],
      ),
      transactionReference: str(
        json['transaction_reference'] ?? json['transactionReference'],
      ),
      notes: str(json['notes']),
      createdAt: parseDate(json['created_at'] ?? json['createdAt']),
      updatedAt: parseDate(json['updated_at'] ?? json['updatedAt']),
    );
  }

  Map<String, dynamic> toJson() => _$TransactionToJson(this);

  // Check if transaction is topup
  bool get isTopup => type == 'topup';

  // Check if transaction is bundle
  bool get isBundle => type == 'bundle';

  // Check if transaction is commission
  bool get isCommission => type == 'commission';

  // Check if transaction is wallet recharge
  bool get isWalletRecharge => type == 'wallet_recharge';

  // Check if transaction is completed
  bool get isCompleted => status == 'completed';

  // Check if transaction is pending
  bool get isPending => status == 'pending';

  // Check if transaction is failed
  bool get isFailed => status == 'failed';
}
