// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transaction_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Transaction _$TransactionFromJson(Map<String, dynamic> json) => Transaction(
  id: (json['id'] as num).toInt(),
  type: json['type'] as String,
  status: json['status'] as String,
  amount: (json['amount'] as num).toDouble(),
  commission: (json['commission'] as num?)?.toDouble(),
  userId: (json['userId'] as num).toInt(),
  agentId: (json['agentId'] as num?)?.toInt(),
  dealerId: (json['dealerId'] as num?)?.toInt(),
  phoneNumber: json['phoneNumber'] as String?,
  operatorId: (json['operatorId'] as num?)?.toInt(),
  packageId: (json['packageId'] as num?)?.toInt(),
  paymentMethod: json['paymentMethod'] as String?,
  paymentReference: json['paymentReference'] as String?,
  transactionReference: json['transactionReference'] as String?,
  notes: json['notes'] as String?,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$TransactionToJson(Transaction instance) =>
    <String, dynamic>{
      'id': instance.id,
      'type': instance.type,
      'status': instance.status,
      'amount': instance.amount,
      'commission': instance.commission,
      'userId': instance.userId,
      'agentId': instance.agentId,
      'dealerId': instance.dealerId,
      'phoneNumber': instance.phoneNumber,
      'operatorId': instance.operatorId,
      'packageId': instance.packageId,
      'paymentMethod': instance.paymentMethod,
      'paymentReference': instance.paymentReference,
      'transactionReference': instance.transactionReference,
      'notes': instance.notes,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };
