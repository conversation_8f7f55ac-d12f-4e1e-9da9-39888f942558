import 'package:json_annotation/json_annotation.dart';
import 'package:telco_bd/features/auth/models/user_model.dart';
import 'package:telco_bd/features/topup/models/operator_model.dart';
import 'package:telco_bd/features/transactions/models/transaction_model.dart';

part 'dashboard_model.g.dart';

@JsonSerializable()
class DashboardData {
  final User user;
  final List<Operator> operators;

  @JsonKey(name: 'recent_transactions')
  final List<Transaction> recentTransactions;

  @JsonKey(name: 'transaction_stats')
  final TransactionStats transactionStats;

  @JsonKey(name: 'popular_bundles')
  final List<OperatorPackage> popularBundles;

  DashboardData({
    required this.user,
    required this.operators,
    required this.recentTransactions,
    required this.transactionStats,
    required this.popularBundles,
  });

  // Robust factory: accept either root-level fields or nested under 'data'
  factory DashboardData.fromJson(Map<String, dynamic> json) {
    final Map<String, dynamic> src =
        (json['data'] is Map<String, dynamic>)
            ? json['data'] as Map<String, dynamic>
            : json;

    List<Operator> parseOperators(dynamic v) {
      if (v is List) {
        return v
            .whereType<Map<String, dynamic>>()
            .map(Operator.fromJson)
            .toList();
      }
      return <Operator>[];
    }

    List<Transaction> parseTransactions(dynamic v) {
      if (v is List) {
        return v
            .whereType<Map<String, dynamic>>()
            .map(Transaction.fromJson)
            .toList();
      }
      return <Transaction>[];
    }

    List<OperatorPackage> parseBundles(dynamic v) {
      if (v is List) {
        return v
            .whereType<Map<String, dynamic>>()
            .map(OperatorPackage.fromJson)
            .toList();
      }
      return <OperatorPackage>[];
    }

    return DashboardData(
      user: User.fromJson(src['user'] as Map<String, dynamic>),
      operators: parseOperators(src['operators']),
      recentTransactions: parseTransactions(
        src['recent_transactions'] ?? src['recentTransactions'],
      ),
      transactionStats: TransactionStats.fromJson(
        (src['transaction_stats'] ??
                src['transactionStats'] ??
                const {'topup': 0, 'bundle': 0, 'deposit': 0, 'withdrawal': 0})
            as Map<String, dynamic>,
      ),
      popularBundles: parseBundles(
        src['popular_bundles'] ?? src['popularBundles'],
      ),
    );
  }

  Map<String, dynamic> toJson() => _$DashboardDataToJson(this);
}

@JsonSerializable()
class TransactionStats {
  final int topup;
  final int bundle;
  final int deposit;
  final int withdrawal;

  TransactionStats({
    required this.topup,
    required this.bundle,
    required this.deposit,
    required this.withdrawal,
  });

  factory TransactionStats.fromJson(Map<String, dynamic> json) {
    int parseInt(dynamic v) {
      if (v is int) return v;
      if (v is String) return int.tryParse(v) ?? 0;
      return 0;
    }

    return TransactionStats(
      topup: parseInt(json['topup']),
      bundle: parseInt(json['bundle']),
      deposit: parseInt(json['deposit']),
      withdrawal: parseInt(json['withdrawal']),
    );
  }

  Map<String, dynamic> toJson() => _$TransactionStatsToJson(this);
}
