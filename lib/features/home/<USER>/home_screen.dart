import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:telco_bd/config/theme_config.dart';
import 'package:telco_bd/features/home/<USER>/dashboard_provider.dart';
import 'package:telco_bd/features/profile/screens/profile_screen.dart';
import 'package:telco_bd/features/topup/screens/topup_screen.dart';
import 'package:telco_bd/features/wallet/screens/wallet_screen.dart';
import 'package:telco_bd/main.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const DashboardScreen(),
    const TopupScreen(),
    const WalletScreen(),
    // Support screen
    const SupportScreen(),
    const ProfileScreen(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) {
          setState(() {
            _currentIndex = index;
          });
        },
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppTheme.primaryColor,
        unselectedItemColor: AppTheme.textSecondaryColor,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.phone_android),
            label: 'Topup',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.account_balance_wallet),
            label: 'Wallet',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.headset_mic),
            label: 'Support',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }
}

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  @override
  void initState() {
    super.initState();
    // Fetch dashboard data when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<DashboardProvider>(
        context,
        listen: false,
      ).fetchDashboardData();
    });
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final dashboardProvider = Provider.of<DashboardProvider>(context);
    final user = authProvider.currentUser;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              // Refresh dashboard data
              dashboardProvider.refreshDashboardData();
            },
          ),
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: () {
              // Show notifications
            },
          ),
        ],
      ),
      body:
          dashboardProvider.isLoading
              ? const Center(child: CircularProgressIndicator())
              : dashboardProvider.error != null
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      dashboardProvider.error ?? 'Error loading dashboard data',
                      style: const TextStyle(color: Colors.red),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    ElevatedButton(
                      onPressed: () => dashboardProvider.refreshDashboardData(),
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              )
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Welcome card
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(16),
                        gradient: const LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppTheme.primaryColor,
                            AppTheme.primaryDarkColor,
                          ],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: AppTheme.primaryColor.withAlpha(76),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                CircleAvatar(
                                  radius: 30,
                                  backgroundColor: Colors.white,
                                  child: Text(
                                    user?.name.substring(0, 1) ?? 'U',
                                    style: const TextStyle(
                                      fontSize: 24,
                                      fontWeight: FontWeight.bold,
                                      color: AppTheme.primaryColor,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Welcome, ${user?.name ?? 'User'}',
                                        style: const TextStyle(
                                          fontSize: 20,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Role: ${_formatRole(user?.role ?? '')}',
                                        style: const TextStyle(
                                          fontSize: 14,
                                          color: Colors.white70,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white.withAlpha(38),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'Current Balance',
                                        style: TextStyle(
                                          fontSize: 14,
                                          color: Colors.white70,
                                        ),
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        '৳ ${user?.balance.toStringAsFixed(2) ?? '0.00'}',
                                        style: const TextStyle(
                                          fontSize: 22,
                                          fontWeight: FontWeight.bold,
                                          color: Colors.white,
                                        ),
                                      ),
                                    ],
                                  ),
                                  ElevatedButton(
                                    onPressed: () {
                                      Navigator.push(
                                        context,
                                        MaterialPageRoute(
                                          builder:
                                              (context) => const WalletScreen(),
                                        ),
                                      );
                                    },
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.white,
                                      foregroundColor: AppTheme.primaryColor,
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 8,
                                      ),
                                    ),
                                    child: const Text('Add Money'),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Quick actions
                    const Text(
                      'Quick Actions',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    GridView.count(
                      crossAxisCount: 2,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      mainAxisSpacing: 16,
                      crossAxisSpacing: 16,
                      children: [
                        _buildActionCard(
                          context,
                          title: 'Mobile Topup',
                          icon: Icons.phone_android,
                          color: AppTheme.primaryColor,
                          onTap: () {
                            // Navigate to topup screen
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const TopupScreen(),
                              ),
                            );
                          },
                        ),
                        _buildActionCard(
                          context,
                          title: 'Buy Bundle',
                          icon: Icons.data_usage,
                          color: Colors.purple,
                          onTap: () {
                            // Navigate to bundle screen
                          },
                        ),
                        _buildActionCard(
                          context,
                          title: 'Drive Offer',
                          icon: Icons.local_offer,
                          color: Colors.orange,
                          onTap: () {
                            // Navigate to topup screen with drive offer tab
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (context) => const TopupScreen(
                                      initialTab: 'drive_offer',
                                    ),
                              ),
                            );
                          },
                        ),
                        _buildActionCard(
                          context,
                          title: 'Wallet',
                          icon: Icons.account_balance_wallet,
                          color: AppTheme.secondaryColor,
                          onTap: () {
                            // Navigate to wallet screen
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const WalletScreen(),
                              ),
                            );
                          },
                        ),
                        _buildActionCard(
                          context,
                          title: 'Transactions',
                          icon: Icons.receipt_long,
                          color: Colors.amber,
                          onTap: () {
                            // Navigate to transactions screen
                          },
                        ),
                        _buildActionCard(
                          context,
                          title: 'Support',
                          icon: Icons.headset_mic,
                          color: Colors.teal,
                          onTap: () {
                            // Navigate to support screen
                            _showComingSoonDialog(context, 'Support');
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Operators
                    const Text(
                      'Operators',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    dashboardProvider.dashboardData?.operators != null &&
                            dashboardProvider
                                .dashboardData!
                                .operators
                                .isNotEmpty
                        ? SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children:
                                dashboardProvider.dashboardData!.operators.map((
                                  operator,
                                ) {
                                  // Determine color based on operator code
                                  Color operatorColor;
                                  switch (operator.code.toUpperCase()) {
                                    case 'GP':
                                      operatorColor = AppTheme.gpColor;
                                      break;
                                    case 'ROBI':
                                      operatorColor = AppTheme.robiColor;
                                      break;
                                    case 'AIRTEL':
                                      operatorColor = AppTheme.airtelColor;
                                      break;
                                    case 'BL':
                                      operatorColor = AppTheme.banglalinkColor;
                                      break;
                                    case 'TT':
                                      operatorColor = AppTheme.teletalkColor;
                                      break;
                                    default:
                                      operatorColor = Colors.grey;
                                  }

                                  return Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 8.0,
                                    ),
                                    child: _buildOperatorCircle(
                                      operator.code,
                                      operatorColor,
                                      () {
                                        // Navigate to topup screen with selected operator
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder:
                                                (context) => TopupScreen(
                                                  selectedOperator: operator,
                                                ),
                                          ),
                                        );
                                      },
                                    ),
                                  );
                                }).toList(),
                          ),
                        )
                        : Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildOperatorCircle('GP', AppTheme.gpColor, () {
                              // Navigate to Grameenphone topup
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const TopupScreen(),
                                ),
                              );
                            }),
                            _buildOperatorCircle(
                              'Robi',
                              AppTheme.robiColor,
                              () {
                                // Navigate to Robi topup
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const TopupScreen(),
                                  ),
                                );
                              },
                            ),
                            _buildOperatorCircle(
                              'Airtel',
                              AppTheme.airtelColor,
                              () {
                                // Navigate to Airtel topup
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const TopupScreen(),
                                  ),
                                );
                              },
                            ),
                            _buildOperatorCircle(
                              'BL',
                              AppTheme.banglalinkColor,
                              () {
                                // Navigate to Banglalink topup
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const TopupScreen(),
                                  ),
                                );
                              },
                            ),
                            _buildOperatorCircle(
                              'TT',
                              AppTheme.teletalkColor,
                              () {
                                // Navigate to Teletalk topup
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                    builder: (context) => const TopupScreen(),
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                    const SizedBox(height: 24),

                    // Recent transactions
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text(
                          'Recent Transactions',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        TextButton.icon(
                          onPressed: () {
                            // Navigate to transactions screen
                          },
                          icon: const Icon(Icons.arrow_forward, size: 16),
                          label: const Text('View All'),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    // Recent transactions list
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.withAlpha(40)),
                      ),
                      child:
                          dashboardProvider.dashboardData?.recentTransactions !=
                                      null &&
                                  dashboardProvider
                                      .dashboardData!
                                      .recentTransactions
                                      .isNotEmpty
                              ? Column(
                                children:
                                    dashboardProvider.dashboardData!.recentTransactions.map((
                                      transaction,
                                    ) {
                                      // Determine icon and color based on transaction type
                                      IconData icon;
                                      Color iconColor;
                                      String title;
                                      String subtitle = '';
                                      bool isPositive = false;

                                      switch (transaction.type) {
                                        case 'topup':
                                          icon = Icons.phone_android;
                                          iconColor = AppTheme.primaryColor;
                                          title = 'Mobile Topup';
                                          subtitle =
                                              transaction.phoneNumber ?? '';
                                          if (transaction.operatorId != null) {
                                            // Find operator name if available
                                            final operator = dashboardProvider
                                                .dashboardData!
                                                .operators
                                                .firstWhere(
                                                  (op) =>
                                                      op.id ==
                                                      transaction.operatorId,
                                                  orElse:
                                                      () =>
                                                          dashboardProvider
                                                              .dashboardData!
                                                              .operators
                                                              .first,
                                                );
                                            subtitle += ' (${operator.name})';
                                          }
                                          isPositive = false;
                                          break;
                                        case 'bundle':
                                          icon = Icons.data_usage;
                                          iconColor = Colors.purple;
                                          title = 'Internet Bundle';
                                          subtitle =
                                              transaction.phoneNumber ?? '';
                                          isPositive = false;
                                          break;
                                        case 'deposit':
                                          icon = Icons.account_balance_wallet;
                                          iconColor = AppTheme.secondaryColor;
                                          title = 'Wallet Recharge';
                                          subtitle =
                                              transaction.paymentMethod ?? '';
                                          if (transaction.paymentReference !=
                                              null) {
                                            subtitle +=
                                                ' - ${transaction.paymentReference}';
                                          }
                                          isPositive = true;
                                          break;
                                        case 'withdrawal':
                                          icon = Icons.money;
                                          iconColor = Colors.red;
                                          title = 'Withdrawal';
                                          subtitle =
                                              transaction.paymentMethod ?? '';
                                          isPositive = false;
                                          break;
                                        default:
                                          icon = Icons.receipt_long;
                                          iconColor = Colors.grey;
                                          title =
                                              transaction.type
                                                  .substring(0, 1)
                                                  .toUpperCase() +
                                              transaction.type.substring(1);
                                          isPositive = false;
                                      }

                                      return Column(
                                        children: [
                                          _buildTransactionItem(
                                            icon: icon,
                                            iconColor: iconColor,
                                            title: title,
                                            subtitle: subtitle,
                                            amount:
                                                '৳${transaction.amount.toStringAsFixed(2)}',
                                            date: _formatDate(
                                              transaction.createdAt,
                                            ),
                                            isPositive: isPositive,
                                          ),
                                          if (dashboardProvider
                                                  .dashboardData!
                                                  .recentTransactions
                                                  .last
                                                  .id !=
                                              transaction.id)
                                            const Divider(height: 1),
                                        ],
                                      );
                                    }).toList(),
                              )
                              : Column(
                                children: [
                                  _buildTransactionItem(
                                    icon: Icons.phone_android,
                                    iconColor: AppTheme.primaryColor,
                                    title: 'Mobile Topup',
                                    subtitle: '*********** (Grameenphone)',
                                    amount: '৳100.00',
                                    date: 'Today, 10:45 AM',
                                    isPositive: false,
                                  ),
                                  const Divider(height: 1),
                                  _buildTransactionItem(
                                    icon: Icons.data_usage,
                                    iconColor: Colors.purple,
                                    title: 'Internet Bundle',
                                    subtitle: '*********** (Grameenphone)',
                                    amount: '৳149.00',
                                    date: 'Yesterday, 3:20 PM',
                                    isPositive: false,
                                  ),
                                  const Divider(height: 1),
                                  _buildTransactionItem(
                                    icon: Icons.account_balance_wallet,
                                    iconColor: AppTheme.secondaryColor,
                                    title: 'Wallet Recharge',
                                    subtitle: 'bKash - ***********',
                                    amount: '৳500.00',
                                    date: 'Yesterday, 2:15 PM',
                                    isPositive: true,
                                  ),
                                ],
                              ),
                    ),
                  ],
                ),
              ),
    );
  }

  Widget _buildActionCard(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: color.withAlpha(25),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, size: 18, color: color),
              ),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOperatorCircle(String label, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(30),
      child: Column(
        children: [
          CircleAvatar(
            radius: 22,
            backgroundColor: color,
            child: Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
          ),
          const SizedBox(height: 4),
          Text(label),
        ],
      ),
    );
  }

  String _formatRole(String role) {
    if (role.isEmpty) return 'Customer';
    return role.substring(0, 1).toUpperCase() + role.substring(1);
  }

  String _formatDate(DateTime date) {
    // Get current date
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateToCheck = DateTime(date.year, date.month, date.day);

    // Format time
    final hour = date.hour > 12 ? date.hour - 12 : date.hour;
    final period = date.hour >= 12 ? 'PM' : 'AM';
    final minute = date.minute.toString().padLeft(2, '0');
    final timeString = '$hour:$minute $period';

    // Check if date is today or yesterday
    if (dateToCheck == today) {
      return 'Today, $timeString';
    } else if (dateToCheck == yesterday) {
      return 'Yesterday, $timeString';
    } else {
      // Format date for older dates
      final day = date.day;
      final month = _getMonthName(date.month);
      return '$day $month, $timeString';
    }
  }

  String _getMonthName(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return months[month - 1];
  }

  @override
  void setState(VoidCallback callback) {
    // This is a workaround for calling setState from a stateless widget
    // In a real app, you would use a state management solution
    callback();
  }

  void _showComingSoonDialog(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('$feature Coming Soon'),
            content: Text(
              'The $feature feature is under development and will be available soon!',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }

  Widget _buildTransactionItem({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
    required String amount,
    required String date,
    required bool isPositive,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: iconColor.withAlpha(25),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: iconColor, size: 24),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                isPositive ? '+$amount' : '-$amount',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isPositive ? AppTheme.secondaryColor : Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                date,
                style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class SupportScreen extends StatelessWidget {
  const SupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Support')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Support options
            const Text(
              'How can we help you?',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // Support categories
            GridView.count(
              crossAxisCount: 2,
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              mainAxisSpacing: 12,
              crossAxisSpacing: 12,
              childAspectRatio: 1.5,
              children: [
                _buildSupportCard(
                  context,
                  'Account Issues',
                  Icons.person,
                  Colors.blue,
                ),
                _buildSupportCard(
                  context,
                  'Payment Problems',
                  Icons.payment,
                  Colors.green,
                ),
                _buildSupportCard(
                  context,
                  'Topup Failed',
                  Icons.phone_android,
                  Colors.orange,
                ),
                _buildSupportCard(
                  context,
                  'Technical Support',
                  Icons.build,
                  Colors.purple,
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Contact options
            const Text(
              'Contact Us',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            _buildContactOption(
              'Customer Service',
              '09612345678',
              Icons.headset_mic,
              AppTheme.primaryColor,
            ),
            const SizedBox(height: 12),
            _buildContactOption(
              'WhatsApp',
              '+88***********',
              Icons.message,
              Colors.green,
            ),
            const SizedBox(height: 12),
            _buildContactOption(
              'Email Support',
              '<EMAIL>',
              Icons.email,
              Colors.red,
            ),

            const SizedBox(height: 24),

            // FAQ section
            const Text(
              'Frequently Asked Questions',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            _buildFaqItem(
              'How do I recharge my account?',
              'You can recharge your account using mobile banking services like bKash, Nagad, or Rocket. Simply select the "Add Money" option from the wallet screen.',
            ),
            _buildFaqItem(
              'What if my topup fails?',
              'If your topup fails, the amount will be refunded to your wallet within 24 hours. If you don\'t receive a refund, please contact our customer support.',
            ),
            _buildFaqItem(
              'How do I change my password?',
              'Go to Profile > Settings > Security > Change Password to update your password.',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSupportCard(
    BuildContext context,
    String title,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          _showComingSoonDialog(context, title);
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 24, color: color),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContactOption(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          // Contact action
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withAlpha(25),
                  shape: BoxShape.circle,
                ),
                child: Icon(icon, size: 20, color: color),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      value,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: 14,
                color: Colors.grey.shade400,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFaqItem(String question, String answer) {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        title: Text(
          question,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Text(
              answer,
              style: TextStyle(fontSize: 13, color: Colors.grey.shade700),
            ),
          ),
        ],
      ),
    );
  }

  void _showComingSoonDialog(BuildContext context, String feature) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text('$feature Coming Soon'),
            content: Text(
              'The $feature feature is under development and will be available soon!',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('OK'),
              ),
            ],
          ),
    );
  }
}
