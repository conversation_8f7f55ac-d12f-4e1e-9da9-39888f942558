import 'package:telco_bd/core/network/api_client.dart';
import 'package:telco_bd/core/utils/constants.dart';
import 'package:telco_bd/features/home/<USER>/dashboard_model.dart';

class DashboardService {
  final ApiClient _apiClient = ApiClient();

  // Get dashboard data
  Future<DashboardData> getDashboardData() async {
    try {
      final response = await _apiClient.get(AppConstants.dashboardEndpoint);
      if (response is Map<String, dynamic>) {
        if (response['success'] == true) {
          final payload =
              response['data'] is Map<String, dynamic>
                  ? response['data'] as Map<String, dynamic>
                  : response;
          return DashboardData.fromJson(payload);
        }

        // Collect error message
        String message =
            response['message']?.toString() ?? 'Failed to load dashboard data';

        // Special handling for authentication errors
        if (message.toLowerCase().contains('unauthenticated')) {
          message =
              'Authentication failed. This appears to be a server-side issue. Please contact support or try again later.';
        }

        if (response['errors'] is Map<String, dynamic>) {
          final errors = response['errors'] as Map<String, dynamic>;
          final first = errors.values.isNotEmpty ? errors.values.first : null;
          if (first is List && first.isNotEmpty) {
            message = first.first.toString();
          }
        }
        throw Exception(message);
      }

      throw Exception('Unexpected response from server');
    } catch (e) {
      // If the error already contains a meaningful message, don't wrap it
      final errorMessage = e.toString();
      if (errorMessage.startsWith('Exception: ')) {
        final cleanMessage = errorMessage.substring('Exception: '.length);
        if (cleanMessage.contains('Authentication failed') ||
            cleanMessage.contains('server-side issue')) {
          throw Exception(cleanMessage);
        }
      }
      throw Exception('Failed to load dashboard data: ${e.toString()}');
    }
  }
}
