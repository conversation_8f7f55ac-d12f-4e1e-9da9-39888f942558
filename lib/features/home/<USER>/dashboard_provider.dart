import 'package:flutter/foundation.dart';
import 'package:telco_bd/features/home/<USER>/dashboard_model.dart';
import 'package:telco_bd/features/home/<USER>/dashboard_service.dart';

class DashboardProvider extends ChangeNotifier {
  final DashboardService _dashboardService = DashboardService();
  
  DashboardData? _dashboardData;
  bool _isLoading = false;
  String? _error;

  DashboardData? get dashboardData => _dashboardData;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Fetch dashboard data
  Future<void> fetchDashboardData() async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      _dashboardData = await _dashboardService.getDashboardData();
      _error = null;
    } catch (e) {
      _error = e.toString();
      if (kDebugMode) {
        print('Dashboard error: $e');
      }
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // Refresh dashboard data
  Future<void> refreshDashboardData() async {
    await fetchDashboardData();
  }
}
