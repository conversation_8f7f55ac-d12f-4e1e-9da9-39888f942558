// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dashboard_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DashboardData _$DashboardDataFromJson(Map<String, dynamic> json) =>
    DashboardData(
      user: User.fromJson(json['user'] as Map<String, dynamic>),
      operators:
          (json['operators'] as List<dynamic>)
              .map((e) => Operator.fromJson(e as Map<String, dynamic>))
              .toList(),
      recentTransactions:
          (json['recent_transactions'] as List<dynamic>)
              .map((e) => Transaction.fromJson(e as Map<String, dynamic>))
              .toList(),
      transactionStats: TransactionStats.fromJson(
        json['transaction_stats'] as Map<String, dynamic>,
      ),
      popularBundles:
          (json['popular_bundles'] as List<dynamic>)
              .map((e) => OperatorPackage.fromJson(e as Map<String, dynamic>))
              .toList(),
    );

Map<String, dynamic> _$DashboardDataToJson(DashboardData instance) =>
    <String, dynamic>{
      'user': instance.user,
      'operators': instance.operators,
      'recent_transactions': instance.recentTransactions,
      'transaction_stats': instance.transactionStats,
      'popular_bundles': instance.popularBundles,
    };

TransactionStats _$TransactionStatsFromJson(Map<String, dynamic> json) =>
    TransactionStats(
      topup: (json['topup'] as num).toInt(),
      bundle: (json['bundle'] as num).toInt(),
      deposit: (json['deposit'] as num).toInt(),
      withdrawal: (json['withdrawal'] as num).toInt(),
    );

Map<String, dynamic> _$TransactionStatsToJson(TransactionStats instance) =>
    <String, dynamic>{
      'topup': instance.topup,
      'bundle': instance.bundle,
      'deposit': instance.deposit,
      'withdrawal': instance.withdrawal,
    };
