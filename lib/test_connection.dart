import 'package:flutter/material.dart';
import 'package:telco_bd/core/network/api_client.dart';
import 'package:telco_bd/config/app_config.dart';

class TestConnectionScreen extends StatefulWidget {
  const TestConnectionScreen({super.key});

  @override
  State<TestConnectionScreen> createState() => _TestConnectionScreenState();
}

class _TestConnectionScreenState extends State<TestConnectionScreen> {
  bool _isLoading = false;
  String _result = '';
  final ApiClient _apiClient = ApiClient();

  Future<void> _testConnection() async {
    setState(() {
      _isLoading = true;
      _result = 'Testing connection to ${AppConfig.apiBaseUrl}...';
    });

    try {
      // Try to make a simple GET request to the API
      final response = await _apiClient.get('/test');
      
      setState(() {
        _isLoading = false;
        _result = 'Connection successful!\nResponse: $response';
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _result = 'Connection failed: ${e.toString()}';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test API Connection'),
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'API URL: ${AppConfig.apiBaseUrl}',
                style: const TextStyle(fontSize: 16),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20),
              ElevatedButton(
                onPressed: _isLoading ? null : _testConnection,
                child: _isLoading
                    ? const CircularProgressIndicator()
                    : const Text('Test Connection'),
              ),
              const SizedBox(height: 20),
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(5),
                ),
                child: Text(
                  _result,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
