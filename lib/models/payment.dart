class Payment {
  final String? id;
  final double amount;
  final String currency;
  final String status;
  final String? paymentUrl;
  final String? referenceId;
  final String? method;
  final String? transactionId;
  final DateTime? createdAt;

  Payment({
    this.id,
    required this.amount,
    required this.currency,
    required this.status,
    this.paymentUrl,
    this.referenceId,
    this.method,
    this.transactionId,
    this.createdAt,
  });

  factory Payment.fromJson(Map<String, dynamic> json) {
    return Payment(
      id: json['id'],
      amount: (json['amount'] is int)
          ? (json['amount'] as int).toDouble()
          : json['amount'] ?? 0.0,
      currency: json['currency'] ?? 'BDT',
      status: json['status'] ?? 'pending',
      paymentUrl: json['payment_url'],
      referenceId: json['reference_id'],
      method: json['method'],
      transactionId: json['transaction_id'],
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'currency': currency,
      'status': status,
      'payment_url': paymentUrl,
      'reference_id': referenceId,
      'method': method,
      'transaction_id': transactionId,
      'created_at': createdAt?.toIso8601String(),
    };
  }
}
