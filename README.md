# TelcoBD - Mobile TopUp Solution for Bangladesh

A complete Mobile TopUp solution with <PERSON><PERSON> backend and Flutter app for Bangladesh.

## Features

### Multi-Level User System
- **Customers**: Recharge/buy bundles
- **Agents**: Earn commissions, manage customers
- **Dealers**: Manage agents, higher commissions
- **Admin**: Full system control

### Core Features
- Recharge for all Bangladeshi operators (GP, Robi, Airtel, Banglalink, Teletalk)
- Data/minutes/SMS bundle purchases
- Automatic commission distribution (configurable %)

### Payment Integration
- bKash/Nagad/Rocket and Manual
- Customer/Agent/Dealer wallet system

## Technical Stack

### Frontend (Flutter)
- Flutter 3.x with role-specific UIs
- State management with Provider and Bloc
- Secure storage for authentication
- Bengali language support

### Backend (<PERSON>vel)
- Lara<PERSON> 10+ with JWT/Sanctum auth
- MySQL database with transaction/commission tracking
- Admin panel using Filament/Backpack
- API documentation

## Getting Started

### Prerequisites
- Flutter 3.x
- Dart 3.x
- PHP 8.x
- Composer
- MySQL

### Installation

#### Flutter App
1. Clone the repository
2. Navigate to the Flutter app directory
3. Run `flutter pub get` to install dependencies
4. Run `flutter run` to start the app

#### Laravel Backend
1. Navigate to the <PERSON>vel backend directory
2. Run `composer install` to install dependencies
3. Copy `.env.example` to `.env` and configure your database
4. Run `php artisan migrate --seed` to set up the database
5. Run `php artisan serve` to start the server

## License
This project is proprietary and confidential.
